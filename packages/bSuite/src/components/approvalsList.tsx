import React, { useState, useEffect, useRef } from "react";
import { Refresh<PERSON>ontrol, SectionList, StyleSheet, View } from "react-native";
import { useIsFocused } from "@react-navigation/native";

// Components
import { CustomText } from "bcomponents";
import ListHeaderSearch from "./ListHeaderSearch";
import SectionHeader from "./sectionHeader";
import DetailedListItem from "./detailedListItem";
import SummaryListItem from "./summaryListItem";

// Styles
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {
  data: any;
  isSummary?: boolean;
  refresh: boolean;
  onRefresh: () => {};
  scrollIndex?: number;
};

const ListEmptyComponent = () => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.listEmptyComponent}>
      <CustomText
        style={{ fontSize: 14, color: color.TEXT_DIMMED, fontStyle: "italic" }}
      >
        No available approval requests
      </CustomText>
    </View>
  );
};

const ApprovalsList = ({
  data,
  isSummary,
  refresh,
  onRefresh,
  scrollIndex,
}: Props) => {
  const { color } = useThemeAwareObject(createStyles);
  const sectionListRef = useRef(null);
  const [layout, setLayout] = useState(null);
  const isFocused = useIsFocused();

  const onLayout = (event) => {
    const { width, height } = event.nativeEvent.layout;

    setLayout({ width, height });
  };

  useEffect(() => {
    if (layout && scrollIndex !== -1 && scrollIndex > 4 && isFocused) {
      try {
        sectionListRef.current?.scrollToLocation({
          sectionIndex: 0,
          itemIndex: scrollIndex,
          animated: true,
        });
      } catch (error) {
        console.log("Error scrolling to index", error);
      }
    }
  }, [layout, isFocused]);

  return (
    <SectionList
      ref={sectionListRef}
      onLayout={onLayout}
      style={{ flex: 1, backgroundColor: color.BACKGROUND }}
      sections={data}
      keyExtractor={(item, index) => item + index}
      renderItem={isSummary ? SummaryListItem : DetailedListItem}
      stickySectionHeadersEnabled={true}
      renderSectionHeader={({ section: { title } }) => (
        <SectionHeader title={title} />
      )}
      ListHeaderComponent={<ListHeaderSearch isSummary={isSummary} />}
      refreshControl={
        <RefreshControl
          tintColor={color.TEXT_DIMMED}
          colors={[color.TEXT_DIMMED]}
          refreshing={refresh}
          onRefresh={onRefresh}
        />
      }
      ListEmptyComponent={ListEmptyComponent}
      getItemLayout={(data, index) => {
        const ITEM_HEIGHT = 110;

        return {
          length: ITEM_HEIGHT,
          offset: ITEM_HEIGHT * index,
          index,
        };
      }}
      onScrollToIndexFailed={(info) => {
        const wait = new Promise((resolve) => setTimeout(resolve, 500));
        wait.then(() => {
          sectionListRef.current?.scrollToLocation({
            sectionIndex: 0,
            itemIndex: info.index,
            animated: true,
          });
        });
      }}
    />
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    listEmptyComponent: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      marginTop: "50%",
    },
  });

  return { styles, color };
};

export default ApprovalsList;
